#include "DataAsset/OceanologyWaterWetnessPresetFactory.h"
#include "DataAsset/OceanologyWaterWetnessPreset.h"

#define LOCTEXT_NAMESPACE "OceanologyEditor"

/**
 * This is an auto-generated class from WaterParent actor via AOceanologyPresetConverter. DO NOT EDIT BY HAND EVER! Your changes will be lost. Edit WaterParent structs instead!
 * Generated at: 28 April 2025
 **/
UOceanologyWaterWetnessPresetFactory::UOceanologyWaterWetnessPresetFactory()
{
	bCreateNew = true;
	bEditAfterNew = true;
	SupportedClass = UOceanologyWaterWetnessPreset::StaticClass();
}

UObject* UOceanologyWaterWetnessPresetFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
	UOceanologyWaterWetnessPreset* OceanologyPreset = NewObject<UOceanologyWaterWetnessPreset>(InParent, Class, Name, Flags);
	return OceanologyPreset;
}

FText UOceanologyWaterWetnessPresetFactory::GetDisplayName() const
{
	return LOCTEXT("OceanologyWaterWetnessPresetText", "Oceanology Water Wetness Preset");
}

FString UOceanologyWaterWetnessPresetFactory::GetDefaultNewAssetName() const
{
	return FString(TEXT("NewWaterWetnessPreset"));}

