// Fill out your copyright notice in the Description page of Project Settings.


#include "Utils/OceanologyManagerDetailsCustomization.h"
#include "AssetToolsModule.h"
#include "DetailLayoutBuilder.h"
#include "DetailWidgetRow.h"
#include "RuntimeVirtualTextureSetBounds.h"
#include "RuntimeVirtualTextureSetBounds.h"
#include "Components/OceanologyHeightmapComponent.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Engine/Texture2D.h"
#include "Factories/Texture2dFactoryNew.h"

#define LOCTEXT_NAMESPACE "OceanologyEditorManager"

TAutoConsoleVariable<int32> CVarOceanologyDevSettings(
	TEXT("oceanology.dev.enable"), 0, TEXT("Enable/Disable recheck of building system weights after every action."));

TSharedRef<IDetailCustomization> FOceanologyManagerDetailsCustomization::MakeInstance()
{
	return MakeShareable(new FOceanologyManagerDetailsCustomization);
}

FOceanologyManagerDetailsCustomization::FOceanologyManagerDetailsCustomization()
{
}

bool FOceanologyManagerDetailsCustomization::IsSetBoundsEnabled() const
{
	return OceanologyManager->HeightmapVirtualTextureComponent->GetVirtualTexture() != nullptr;
}

FReply FOceanologyManagerDetailsCustomization::SetBounds()
{
	if (OceanologyManager->HeightmapVirtualTextureComponent->GetVirtualTexture() != nullptr)
	{
		const FScopedTransaction Transaction(LOCTEXT("Transaction_SetBounds",
		                                             "Set RuntimeVirtualTextureComponent Bounds"));

		RuntimeVirtualTexture::SetBounds(OceanologyManager->HeightmapVirtualTextureComponent);

		// Force update of editor view widget.
		GEditor->NoteSelectionChange(false);

		return FReply::Handled();
	}
	return FReply::Unhandled();
}

FReply FOceanologyManagerDetailsCustomization::SetupLandscapeOrGroundMesh()
{
	OceanologyManager->HeightmapComponent->SetupLandscapeOrGroundMesh();

	return FReply::Handled();
}

void FOceanologyManagerDetailsCustomization::RefreshTextDetails()
{
}

void FOceanologyManagerDetailsCustomization::RefreshDetailsView()
{
}

void FOceanologyManagerDetailsCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
	if (!CVarOceanologyDevSettings.GetValueOnAnyThread())
	{
		DetailBuilder.HideCategory("RuntimeVirtualTexture");
		DetailBuilder.HideCategory("StreamingVirtualTexture");
		DetailBuilder.HideCategory("Bounds");
		DetailBuilder.HideCategory("Niagara");
		DetailBuilder.HideCategory("GlobalHeightmapNiagaraComponent");
		DetailBuilder.HideCategory("LocalHeightmapNiagaraComponent");
	}

	TArray<TWeakObjectPtr<UObject>> ObjectsBeingCustomized;
	DetailBuilder.GetObjectsBeingCustomized(ObjectsBeingCustomized);
	if (ObjectsBeingCustomized.Num() > 1)
	{
		return;
	}

	OceanologyManager = Cast<AOceanologyManager>(ObjectsBeingCustomized[0].Get());
	if (!OceanologyManager)
	{
		return;
	}

	EditHeightmapCategory(DetailBuilder);
	EditBoundsCategory(DetailBuilder);
}

void FOceanologyManagerDetailsCustomization::EditHeightmapCategory(IDetailLayoutBuilder& DetailBuilder)
{
	IDetailCategoryBuilder& Category = DetailBuilder.EditCategory("Heightmap");
	TArray<TSharedRef<IPropertyHandle>> Properties;
	Category.GetDefaultProperties(Properties);

	Category.AddCustomRow(INVTEXT("Setup Landscape"))
	        .NameContent()
		[
			SNew(STextBlock)
			.Font(IDetailLayoutBuilder::GetDetailFont())
			.Text(LOCTEXT("Button_SetupLandscapeGroundMeshButton", "Setup Landscape/Ground Mesh"))
			.ToolTipText(LOCTEXT("Button_SetupLandscapeGroundMeshButton_Tooltip", "Save the heightmap texture"))
		]
		.ValueContent()
		[
			SNew(SHorizontalBox)
			+ SHorizontalBox::Slot()
			.FillWidth(4.0f)
			[
				SNew(SButton)
				.HAlign(HAlign_Center)
				.VAlign(VAlign_Center)
				.ContentPadding(2)
				.Text(LOCTEXT("Button_SetupLandscapeGroundMeshButton", "Setup Landscape/Ground Mesh"))
				.OnClicked(this, &FOceanologyManagerDetailsCustomization::SetupLandscapeOrGroundMesh)
			]
		];

	for (TSharedRef<IPropertyHandle> Property : Properties)
	{
		Category.AddProperty(Property);
	}
}

void FOceanologyManagerDetailsCustomization::EditBoundsCategory(IDetailLayoutBuilder& DetailBuilder)
{
	// Apply custom widget for SetBounds.
	TSharedRef<IPropertyHandle> SetBoundsPropertyHandle = DetailBuilder.GetProperty(TEXT("bSetAllBoundsButton"));
	DetailBuilder.EditDefaultProperty(SetBoundsPropertyHandle)->CustomWidget()
	             .NameContent()
		[
			SNew(STextBlock)
			.Font(IDetailLayoutBuilder::GetDetailFont())
			.Text(LOCTEXT("Button_SetBounds", "Set Bounds"))
			.ToolTipText(LOCTEXT("Button_SetBounds_Tooltip",
			                     "Set the rotation to match the Bounds Align Actor and expand bounds to include all primitives that write to this virtual texture."))
		]
		.ValueContent()
		.MinDesiredWidth(125.f)
		[
			SNew(SButton)
			.VAlign(VAlign_Center)
			.HAlign(HAlign_Center)
			.ContentPadding(2)
			.Text(LOCTEXT("Button_SetBounds", "Set Bounds"))
			.OnClicked(this, &FOceanologyManagerDetailsCustomization::SetBounds)
			.IsEnabled(this, &FOceanologyManagerDetailsCustomization::IsSetBoundsEnabled)
		];
}


#undef LOCTEXT_NAMESPACE
