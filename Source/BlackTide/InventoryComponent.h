// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "InventoryItem.h"
#include "Net/UnrealNetwork.h"
#include "InventoryComponent.generated.h"

// Forward declarations
class ABlack<PERSON>ideCharacter;
class USurvivalComponent;

// LogInventory is now defined as static in InventoryComponent.cpp

/**
 * Structure representing a single inventory slot
 */
USTRUCT(BlueprintType)
struct BLACKTIDE_API FInventorySlot
{
	GENERATED_BODY()

	// The item in this slot (invalid item = empty slot)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
	FInventoryItem Item;

	// Grid position (X, Y coordinates in the grid)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
	int32 GridX = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
	int32 GridY = 0;

	// Note: Slot occupancy is determined by Item.IsValid() only

	FInventorySlot()
	{
		Item = FInventoryItem();
		GridX = 0;
		GridY = 0;
	}

	FInventorySlot(int32 X, int32 Y)
	{
		Item = FInventoryItem();
		GridX = X;
		GridY = Y;
	}

	bool IsEmpty() const
	{
		return !Item.IsValid();
	}
};

/**
 * Delegates for inventory events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInventoryChanged, int32, SlotIndex, const FInventoryItem&, Item);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnItemAdded, int32, SlotIndex, const FInventoryItem&, Item, int32, Amount);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnItemRemoved, int32, SlotIndex, const FInventoryItem&, Item, int32, Amount);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInventoryFull, const FInventoryItem&, Item, int32, Amount);

/**
 * Grid-based inventory component for storing and managing items
 */
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class BLACKTIDE_API UInventoryComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UInventoryComponent();

protected:
	virtual void BeginPlay() override;
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
	// Grid dimensions
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory", meta = (ClampMin = "1", ClampMax = "20"))
	int32 GridWidth = 4;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory", meta = (ClampMin = "1", ClampMax = "20"))
	int32 GridHeight = 4;

	// Maximum weight capacity
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory", meta = (ClampMin = "0.0"))
	float MaxWeight = 100.0f;

	// Inventory slots (replicated)
	UPROPERTY(ReplicatedUsing = OnRep_InventorySlots, BlueprintReadOnly, Category = "Inventory")
	TArray<FInventorySlot> InventorySlots;

	// Equipment system
	UPROPERTY(BlueprintReadOnly, Category = "Equipment", Replicated)
	int32 ActiveToolSlotIndex = -1;  // -1 = no tool equipped

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Inventory Events")
	FOnInventoryChanged OnInventoryChanged;

	UPROPERTY(BlueprintAssignable, Category = "Inventory Events")
	FOnItemAdded OnItemAdded;

	UPROPERTY(BlueprintAssignable, Category = "Inventory Events")
	FOnItemRemoved OnItemRemoved;

	UPROPERTY(BlueprintAssignable, Category = "Inventory Events")
	FOnInventoryFull OnInventoryFull;

	// Core inventory functions
	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool AddItem(const FInventoryItem& Item, int32& OutSlotIndex);

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool AddItemToSlot(const FInventoryItem& Item, int32 SlotIndex);

	// Equipment integration
	UFUNCTION(BlueprintCallable, Category = "Equipment")
	bool TryEquipItem(int32 SlotIndex);

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	bool TryUnequipItem(int32 SlotIndex);

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	void HandleRightClickOnSlot(int32 SlotIndex);

	// Debug functions
	UFUNCTION(BlueprintCallable, Category = "Inventory Debug")
	void DebugSlotStates() const;

	UFUNCTION(BlueprintCallable, Category = "Inventory Debug")
	void VerifySlotIntegrity() const;

	UFUNCTION(BlueprintCallable, Category = "Inventory Debug")
	bool ValidateInventoryState() const;

	UFUNCTION(BlueprintCallable, Category = "Inventory Debug")
	void FixInventoryInconsistencies();

	UFUNCTION(BlueprintCallable, Category = "Inventory Debug")
	void DebugEventBindings() const;

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool RemoveItem(int32 SlotIndex, int32 Amount = -1);

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool MoveItem(int32 FromSlotIndex, int32 ToSlotIndex);

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool SwapItems(int32 SlotIndexA, int32 SlotIndexB);

	// Query functions
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	FInventoryItem GetItemAtSlot(int32 SlotIndex) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	bool IsSlotEmpty(int32 SlotIndex) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	bool CanPlaceItemAtSlot(const FInventoryItem& Item, int32 SlotIndex) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	int32 FindEmptySlotForItem(const FInventoryItem& Item) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	int32 GetTotalSlots() const { return GridWidth * GridHeight; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	float GetCurrentWeight() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	float GetWeightPercent() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	bool IsOverweight() const;

	// Grid utility functions
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	int32 GetSlotIndex(int32 GridX, int32 GridY) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	void GetGridCoordinates(int32 SlotIndex, int32& OutGridX, int32& OutGridY) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	bool IsValidGridPosition(int32 GridX, int32 GridY) const;

	// Stack management
	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool TryStackItem(const FInventoryItem& Item, int32& OutSlotIndex);

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	FInventoryItem SplitStack(int32 SlotIndex, int32 Amount);

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool ConsumeItem(int32 SlotIndex);

	// Initialization
	UFUNCTION(BlueprintCallable, Category = "Inventory")
	void ForceInitializeInventory();

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	bool IsInventoryInitialized() const;

	// Server functions
	UFUNCTION(Server, Reliable, Category = "Inventory")
	void ServerAddItem(const FInventoryItem& Item);

	UFUNCTION(Server, Reliable, Category = "Inventory")
	void ServerRemoveItem(int32 SlotIndex, int32 Amount);

	UFUNCTION(Server, Reliable, Category = "Inventory")
	void ServerMoveItem(int32 FromSlotIndex, int32 ToSlotIndex);

	UFUNCTION(Server, Reliable, WithValidation, Category = "Inventory")
	void ServerConsumeItem(int32 SlotIndex);

	// Equipment functions
	UFUNCTION(BlueprintCallable, Category = "Equipment")
	bool EquipTool(int32 SlotIndex);

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	void UnequipTool();

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Equipment")
	FInventoryItem GetEquippedTool() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Equipment")
	bool HasToolEquipped() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Equipment")
	int32 GetEquippedToolSlotIndex() const { return ActiveToolSlotIndex; }

	// Item counting and removal by ID (for crafting system)
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Inventory")
	int32 GetItemCount(const FString& ItemID) const;

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool RemoveItemByID(const FString& ItemID, int32 Amount = 1);

protected:
	// Replication
	UFUNCTION()
	void OnRep_InventorySlots();

	// Internal functions
	void InitializeInventory();
	bool PlaceItemAtSlot(const FInventoryItem& Item, int32 SlotIndex);
	void ClearSlot(int32 SlotIndex);
	bool HasSpaceForItem(const FInventoryItem& Item, int32 SlotIndex) const;

private:
	// Cache for performance
	mutable float CachedWeight = -1.0f;
	mutable bool bWeightCacheDirty = true;

	// Cache for stackable items optimization
	mutable TMap<FString, TArray<int32>> StackableItemSlots;
	mutable bool bStackableCacheDirty = true;

	void InvalidateWeightCache() { bWeightCacheDirty = true; }
	void InvalidateStackableCache() { bStackableCacheDirty = true; }
	void UpdateStackableCache() const;
};
