// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/Engine.h"
#include "BlackTideGatheringComponent.generated.h"

class ABlackTideCharacter;
class UBlackTideInteractableComponent;

/**
 * Delegates for gathering events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnGatheringStarted, AActor*, TargetActor, float, Duration);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGatheringCompleted, AActor*, TargetActor);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGatheringCancelled, AActor*, TargetActor);

/**
 * Component that handles resource gathering for BlackTide characters
 * Manages interaction detection, line tracing, and gathering state
 */
UCLASS(ClassGroup=(BlackTide), meta=(BlueprintSpawnableComponent))
class BLACKTIDE_API UBlackTideGatheringComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UBlackTideGatheringComponent();

protected:
	virtual void BeginPlay() override;
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
	// Gathering settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gathering")
	float GatheringRange = 300.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gathering")
	float InteractionCheckInterval = 0.1f; // How often to check for interactables

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Gathering Events")
	FOnGatheringStarted OnGatheringStarted;

	UPROPERTY(BlueprintAssignable, Category = "Gathering Events")
	FOnGatheringCompleted OnGatheringCompleted;

	UPROPERTY(BlueprintAssignable, Category = "Gathering Events")
	FOnGatheringCancelled OnGatheringCancelled;

	// Core gathering functions
	UFUNCTION(BlueprintCallable, Category = "Gathering")
	bool StartGathering();

	UFUNCTION(BlueprintCallable, Category = "Gathering")
	void StopGathering();

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Gathering")
	bool IsGathering() const { return bIsCurrentlyGathering; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Gathering")
	AActor* GetCurrentTarget() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Gathering")
	UBlackTideInteractableComponent* GetCurrentInteractable() const { return CurrentTargetInteractable; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Gathering")
	float GetGatheringProgress() const;

	// Detection functions
	UFUNCTION(BlueprintCallable, Category = "Gathering")
	UBlackTideInteractableComponent* FindNearestInteractable() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Gathering")
	FString GetInteractionPrompt() const;

	// Server functions
	UFUNCTION(Server, Reliable, WithValidation, Category = "Gathering")
	void ServerStartGathering();

	UFUNCTION(Server, Reliable, WithValidation, Category = "Gathering")
	void ServerStopGathering();

	// Server RPC to handle interactable objects (like trees) that don't have owning connections
	UFUNCTION(Server, Reliable, WithValidation, Category = "Gathering")
	void ServerStartInteractionWithObject(class UBlackTideInteractableComponent* InteractableComponent, const FString& EquippedToolID);

protected:
	// Replicated gathering state
	UPROPERTY(Replicated, BlueprintReadOnly, Category = "Gathering State")
	bool bIsCurrentlyGathering = false;

	UPROPERTY(Replicated, BlueprintReadOnly, Category = "Gathering State")
	AActor* CurrentTargetActor = nullptr;

	// Internal state
	UPROPERTY()
	UBlackTideInteractableComponent* CurrentTargetInteractable;

	UPROPERTY()
	ABlackTideCharacter* OwnerCharacter;

	float LastInteractionCheck;

	// Internal functions
	void UpdateInteractionDetection();
	UBlackTideInteractableComponent* PerformInteractionTrace() const;
	UBlackTideInteractableComponent* PerformSphereOverlapDetection() const;
	void OnInteractionCompleted(ABlackTideCharacter* Character);
	void OnInteractionCancelled(ABlackTideCharacter* Character);

	// Bind to interactable events
	UFUNCTION()
	void HandleInteractionCompleted(ABlackTideCharacter* Character);

	UFUNCTION()
	void HandleInteractionCancelled(ABlackTideCharacter* Character);
};
