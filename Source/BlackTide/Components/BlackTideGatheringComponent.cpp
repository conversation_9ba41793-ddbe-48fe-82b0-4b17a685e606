// Copyright Epic Games, Inc. All Rights Reserved.

#include "BlackTideGatheringComponent.h"
#include "../BlackTideCharacter.h"
#include "BlackTideInteractableComponent.h"
#include "BlackTideEquipmentComponent.h"
#include "../Weapons/BlackTideWeaponBase.h"
#include "../Actors/BlackTideResourceNode.h"
#include "../Actors/BlackTideWorldItem.h"
#include "Engine/World.h"
#include "Engine/OverlapResult.h"
#include "DrawDebugHelpers.h"
#include "Camera/CameraComponent.h"
#include "Net/UnrealNetwork.h"

DEFINE_LOG_CATEGORY_STATIC(LogBlackTideGathering, Log, All);

UBlackTideGatheringComponent::UBlackTideGatheringComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	PrimaryComponentTick.bStartWithTickEnabled = true;
	SetIsReplicatedByDefault(true);

	// Default settings
	GatheringRange = 300.0f;
	InteractionCheckInterval = 0.1f;

	// Initialize state
	CurrentTargetInteractable = nullptr;
	OwnerCharacter = nullptr;
	LastInteractionCheck = 0.0f;
	bIsCurrentlyGathering = false;
	CurrentTargetActor = nullptr;
}

void UBlackTideGatheringComponent::BeginPlay()
{
	Super::BeginPlay();

	// Cache owner character
	OwnerCharacter = Cast<ABlackTideCharacter>(GetOwner());
	if (!OwnerCharacter)
	{
		UE_LOG(LogBlackTideGathering, Error, TEXT("GatheringComponent must be attached to a BlackTideCharacter!"));
	}
}

void UBlackTideGatheringComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME_CONDITION(UBlackTideGatheringComponent, bIsCurrentlyGathering, COND_SkipOwner);
	DOREPLIFETIME_CONDITION(UBlackTideGatheringComponent, CurrentTargetActor, COND_SkipOwner);
}

void UBlackTideGatheringComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (!OwnerCharacter)
	{
		return;
	}

	// Periodically check for nearby interactables
	float CurrentTime = GetWorld()->GetTimeSeconds();
	if (CurrentTime - LastInteractionCheck >= InteractionCheckInterval)
	{
		UpdateInteractionDetection();
		LastInteractionCheck = CurrentTime;
	}
}

bool UBlackTideGatheringComponent::StartGathering()
{
	UE_LOG(LogBlackTideGathering, Warning, TEXT("🎯 StartGathering called"));

	// Client calls server
	if (GetOwnerRole() != ROLE_Authority)
	{
		UE_LOG(LogBlackTideGathering, Warning, TEXT("📡 Client calling server RPC"));
		ServerStartGathering();
		return true; // Assume success for client
	}

	UE_LOG(LogBlackTideGathering, Warning, TEXT("🖥️ Server executing StartGathering"));

	// Server execution
	if (!OwnerCharacter)
	{
		UE_LOG(LogBlackTideGathering, Warning, TEXT("❌ Cannot start gathering - no owner character"));
		return false;
	}

	// Allow multiple gathering attempts - let the interaction system handle duplicates

	// Find nearest interactable
	UE_LOG(LogBlackTideGathering, Warning, TEXT("🔍 Searching for nearest interactable..."));
	UBlackTideInteractableComponent* TargetInteractable = FindNearestInteractable();
	if (!TargetInteractable)
	{
		UE_LOG(LogBlackTideGathering, Warning, TEXT("❌ No interactable found in range"));
		return false;
	}

	UE_LOG(LogBlackTideGathering, Warning, TEXT("✅ Found interactable: %s"),
		*TargetInteractable->GetOwner()->GetName());

	// Try to start interaction
	UE_LOG(LogBlackTideGathering, Warning, TEXT("🚀 Attempting to start interaction..."));
	if (!TargetInteractable->StartInteraction(OwnerCharacter))
	{
		UE_LOG(LogBlackTideGathering, Warning, TEXT("❌ Failed to start interaction"));
		return false;
	}

	UE_LOG(LogBlackTideGathering, Warning, TEXT("✅ Interaction started successfully"));

	// Set current target and bind events
	CurrentTargetInteractable = TargetInteractable;
	CurrentTargetActor = TargetInteractable->GetOwner();
	bIsCurrentlyGathering = true;

	// Bind to interaction events
	TargetInteractable->OnInteractionCompleted.AddDynamic(this, &UBlackTideGatheringComponent::HandleInteractionCompleted);
	TargetInteractable->OnInteractionCancelled.AddDynamic(this, &UBlackTideGatheringComponent::HandleInteractionCancelled);

	UE_LOG(LogBlackTideGathering, Log, TEXT("Started gathering from %s"), *TargetInteractable->GetOwner()->GetName());

	// Broadcast event
	OnGatheringStarted.Broadcast(TargetInteractable->GetOwner(), TargetInteractable->InteractionDuration);

	return true;
}

void UBlackTideGatheringComponent::StopGathering()
{
	UE_LOG(LogBlackTideGathering, Warning, TEXT("🛑 StopGathering called"));

	// Client calls server
	if (GetOwnerRole() != ROLE_Authority)
	{
		UE_LOG(LogBlackTideGathering, Warning, TEXT("📡 Client calling server RPC"));
		ServerStopGathering();
		return;
	}

	UE_LOG(LogBlackTideGathering, Warning, TEXT("🖥️ Server executing StopGathering"));

	// Server execution - extra safety checks
	if (!CurrentTargetInteractable)
	{
		UE_LOG(LogBlackTideGathering, Warning, TEXT("⚠️ CurrentTargetInteractable is null, cleaning up state"));
		CurrentTargetInteractable = nullptr;
		CurrentTargetActor = nullptr;
		bIsCurrentlyGathering = false;
		return;
	}

	if (!IsValid(CurrentTargetInteractable))
	{
		UE_LOG(LogBlackTideGathering, Warning, TEXT("⚠️ CurrentTargetInteractable is invalid, cleaning up state"));
		CurrentTargetInteractable = nullptr;
		CurrentTargetActor = nullptr;
		bIsCurrentlyGathering = false;
		return;
	}

	// Store references before doing anything that might invalidate them
	AActor* TargetActor = CurrentTargetInteractable->GetOwner();
	UBlackTideInteractableComponent* InteractableToCleanup = CurrentTargetInteractable;

	// Clear our reference first to prevent re-entry
	CurrentTargetInteractable = nullptr;
	CurrentTargetActor = nullptr;
	bIsCurrentlyGathering = false;

	// Now safely unbind events using the stored reference
	if (InteractableToCleanup && IsValid(InteractableToCleanup))
	{
		// Cancel the interaction
		InteractableToCleanup->CancelInteraction();

		// Safely unbind events
		if (InteractableToCleanup->OnInteractionCompleted.IsBound())
		{
			InteractableToCleanup->OnInteractionCompleted.RemoveDynamic(this, &UBlackTideGatheringComponent::HandleInteractionCompleted);
		}
		if (InteractableToCleanup->OnInteractionCancelled.IsBound())
		{
			InteractableToCleanup->OnInteractionCancelled.RemoveDynamic(this, &UBlackTideGatheringComponent::HandleInteractionCancelled);
		}
	}

	UE_LOG(LogBlackTideGathering, Log, TEXT("Stopped gathering"));

	// Broadcast event
	OnGatheringCancelled.Broadcast(TargetActor);
}

AActor* UBlackTideGatheringComponent::GetCurrentTarget() const
{
	return CurrentTargetInteractable ? CurrentTargetInteractable->GetOwner() : nullptr;
}

float UBlackTideGatheringComponent::GetGatheringProgress() const
{
	return CurrentTargetInteractable ? CurrentTargetInteractable->GetInteractionProgress() : 0.0f;
}

UBlackTideInteractableComponent* UBlackTideGatheringComponent::FindNearestInteractable() const
{
	if (!OwnerCharacter)
	{
		return nullptr;
	}

	// Use line trace from camera
	return PerformInteractionTrace();
}

FString UBlackTideGatheringComponent::GetInteractionPrompt() const
{
	UBlackTideInteractableComponent* NearestInteractable = FindNearestInteractable();
	if (!NearestInteractable)
	{
		return FString();
	}

	if (!NearestInteractable->CanInteract(OwnerCharacter))
	{
		return FString();
	}

	return FString::Printf(TEXT("[E] %s"), *NearestInteractable->InteractionText);
}

void UBlackTideGatheringComponent::UpdateInteractionDetection()
{
	// This could be used for UI updates or other periodic checks
	// For now, we'll keep it simple
}

UBlackTideInteractableComponent* UBlackTideGatheringComponent::PerformInteractionTrace() const
{
	if (!OwnerCharacter)
	{
		return nullptr;
	}

	// Get camera component for accurate aiming
	UCameraComponent* Camera = OwnerCharacter->FindComponentByClass<UCameraComponent>();
	if (!Camera)
	{
		UE_LOG(LogBlackTideGathering, Warning, TEXT("No camera component found on character"));
		return nullptr;
	}

	// Setup trace parameters - use character's forward direction
	FVector Start = OwnerCharacter->GetActorLocation();
	FVector Forward = OwnerCharacter->GetActorForwardVector();

	// Adjust start position to be at chest level and slightly forward
	Start = Start + FVector(0, 0, 50.0f) + (Forward * 30.0f); // Chest level + 30cm forward
	FVector End = Start + (Forward * GatheringRange);

	FHitResult HitResult;
	FCollisionQueryParams QueryParams;
	QueryParams.AddIgnoredActor(OwnerCharacter);

	// Perform line trace - try multiple channels to find different object types
	bool bHit = GetWorld()->LineTraceSingleByChannel(
		HitResult,
		Start,
		End,
		ECC_Visibility, // First try visibility
		QueryParams
	);

	// If that didn't work, try WorldStatic (for resource nodes)
	if (!bHit)
	{
		bHit = GetWorld()->LineTraceSingleByChannel(
			HitResult,
			Start,
			End,
			ECC_WorldStatic,
			QueryParams
		);
	}

	// If still no hit, try WorldDynamic (for world items like logs)
	if (!bHit)
	{
		bHit = GetWorld()->LineTraceSingleByChannel(
			HitResult,
			Start,
			End,
			ECC_WorldDynamic,
			QueryParams
		);
	}

	// Debug draw the trace - always show in development
	DrawDebugLine(GetWorld(), Start, End, bHit ? FColor::Green : FColor::Red, false, 2.0f, 0, 3.0f);

	UE_LOG(LogBlackTideGathering, Warning, TEXT("Line trace: Start=%s, End=%s, Hit=%s"),
		*Start.ToString(), *End.ToString(), bHit ? TEXT("YES") : TEXT("NO"));

	if (bHit)
	{
		UE_LOG(LogBlackTideGathering, Warning, TEXT("Hit Actor: %s (Class: %s)"),
			HitResult.GetActor() ? *HitResult.GetActor()->GetName() : TEXT("NULL"),
			HitResult.GetActor() ? *HitResult.GetActor()->GetClass()->GetName() : TEXT("NULL"));
	}
	else
	{
		// If no hit, let's check if there are ANY WorldItems nearby using sphere overlap
		TArray<FOverlapResult> OverlapResults;
		FCollisionQueryParams OverlapParams;
		OverlapParams.AddIgnoredActor(OwnerCharacter);

		bool bFoundOverlaps = GetWorld()->OverlapMultiByObjectType(
			OverlapResults,
			Start,
			FQuat::Identity,
			FCollisionObjectQueryParams(ECC_WorldDynamic),
			FCollisionShape::MakeSphere(GatheringRange),
			OverlapParams
		);

		UE_LOG(LogBlackTideGathering, Warning, TEXT("🔍 Sphere overlap check: Found %d WorldDynamic objects in range"), OverlapResults.Num());

		for (const FOverlapResult& Result : OverlapResults)
		{
			if (AActor* FoundActor = Result.GetActor())
			{
				UE_LOG(LogBlackTideGathering, Warning, TEXT("🔍 Found nearby actor: %s (Class: %s)"),
					*FoundActor->GetName(), *FoundActor->GetClass()->GetName());

				if (ABlackTideWorldItem* WorldItem = Cast<ABlackTideWorldItem>(FoundActor))
				{
					UE_LOG(LogBlackTideGathering, Warning, TEXT("🪵 Found WorldItem nearby but line trace missed it!"));

					// Check if it has InteractableComponent
					UBlackTideInteractableComponent* InteractableComp = WorldItem->FindComponentByClass<UBlackTideInteractableComponent>();
					if (InteractableComp)
					{
						UE_LOG(LogBlackTideGathering, Warning, TEXT("✅ WorldItem HAS InteractableComponent - using it directly!"));
						// Since line trace failed but we found a valid WorldItem with InteractableComponent, use it
						return InteractableComp;
					}
					else
					{
						UE_LOG(LogBlackTideGathering, Warning, TEXT("❌ WorldItem has NO InteractableComponent!"));
					}
				}
			}
		}
	}

	if (bHit && HitResult.GetActor())
	{
		// Look for interactable component
		UBlackTideInteractableComponent* InteractableComp = HitResult.GetActor()->FindComponentByClass<UBlackTideInteractableComponent>();

		if (InteractableComp)
		{
			UE_LOG(LogBlackTideGathering, Warning, TEXT("✅ Found InteractableComponent on: %s"), *HitResult.GetActor()->GetName());
		}
		else
		{
			UE_LOG(LogBlackTideGathering, Warning, TEXT("❌ Hit actor %s but NO InteractableComponent found"), *HitResult.GetActor()->GetName());
		}

		return InteractableComp;
	}

	return nullptr;
}

void UBlackTideGatheringComponent::HandleInteractionCompleted(ABlackTideCharacter* Character)
{
	if (Character != OwnerCharacter)
	{
		return;
	}

	AActor* TargetActor = CurrentTargetInteractable ? CurrentTargetInteractable->GetOwner() : nullptr;

	// Safely unbind events
	if (CurrentTargetInteractable && IsValid(CurrentTargetInteractable))
	{
		if (CurrentTargetInteractable->OnInteractionCompleted.IsBound())
		{
			CurrentTargetInteractable->OnInteractionCompleted.RemoveDynamic(this, &UBlackTideGatheringComponent::HandleInteractionCompleted);
		}
		if (CurrentTargetInteractable->OnInteractionCancelled.IsBound())
		{
			CurrentTargetInteractable->OnInteractionCancelled.RemoveDynamic(this, &UBlackTideGatheringComponent::HandleInteractionCancelled);
		}
	}

	// CRITICAL: Clear gathering state
	CurrentTargetInteractable = nullptr;
	CurrentTargetActor = nullptr;
	bIsCurrentlyGathering = false;

	UE_LOG(LogBlackTideGathering, Warning, TEXT("🎉 GATHERING: Interaction completed, state cleared"));

	CurrentTargetInteractable = nullptr;
	CurrentTargetActor = nullptr;
	bIsCurrentlyGathering = false;

	UE_LOG(LogBlackTideGathering, Log, TEXT("Gathering completed"));

	// Broadcast event
	OnGatheringCompleted.Broadcast(TargetActor);
}

void UBlackTideGatheringComponent::HandleInteractionCancelled(ABlackTideCharacter* Character)
{
	if (Character != OwnerCharacter)
	{
		return;
	}

	AActor* TargetActor = CurrentTargetInteractable ? CurrentTargetInteractable->GetOwner() : nullptr;

	// Safely unbind events
	if (CurrentTargetInteractable && IsValid(CurrentTargetInteractable))
	{
		if (CurrentTargetInteractable->OnInteractionCompleted.IsBound())
		{
			CurrentTargetInteractable->OnInteractionCompleted.RemoveDynamic(this, &UBlackTideGatheringComponent::HandleInteractionCompleted);
		}
		if (CurrentTargetInteractable->OnInteractionCancelled.IsBound())
		{
			CurrentTargetInteractable->OnInteractionCancelled.RemoveDynamic(this, &UBlackTideGatheringComponent::HandleInteractionCancelled);
		}
	}

	CurrentTargetInteractable = nullptr;
	CurrentTargetActor = nullptr;
	bIsCurrentlyGathering = false;

	UE_LOG(LogBlackTideGathering, Log, TEXT("Gathering cancelled"));

	// Broadcast event
	OnGatheringCancelled.Broadcast(TargetActor);
}

// Server RPC implementations
bool UBlackTideGatheringComponent::ServerStartGathering_Validate()
{
	return true;
}

void UBlackTideGatheringComponent::ServerStartGathering_Implementation()
{
	StartGathering();
}

bool UBlackTideGatheringComponent::ServerStopGathering_Validate()
{
	return true;
}

void UBlackTideGatheringComponent::ServerStopGathering_Implementation()
{
	StopGathering();
}

bool UBlackTideGatheringComponent::ServerStartInteractionWithObject_Validate(UBlackTideInteractableComponent* InteractableComponent, const FString& EquippedToolID)
{
	return InteractableComponent != nullptr && OwnerCharacter != nullptr;
}

void UBlackTideGatheringComponent::ServerStartInteractionWithObject_Implementation(UBlackTideInteractableComponent* InteractableComponent, const FString& EquippedToolID)
{
	if (!InteractableComponent || !OwnerCharacter)
	{
		UE_LOG(LogBlackTideGathering, Warning, TEXT("❌ SERVER: Invalid interactable or character"));
		return;
	}

	UE_LOG(LogBlackTideGathering, Warning, TEXT("🖥️ SERVER: Starting interaction with %s via character connection (Tool: %s)"),
		*InteractableComponent->GetOwner()->GetName(), *EquippedToolID);

	// Log server's equipped tool state for debugging
	if (UBlackTideEquipmentComponent* EquipmentComp = OwnerCharacter->FindComponentByClass<UBlackTideEquipmentComponent>())
	{
		FInventoryItem MainHandItem = EquipmentComp->GetEquippedItem(EEquipmentSlot::MainHand);
		FString ServerTool = MainHandItem.IsValid() ? MainHandItem.ItemID : TEXT("None");
		UE_LOG(LogBlackTideGathering, Warning, TEXT("🔧 SERVER: Client tool '%s', Server sees '%s'"),
			*EquippedToolID, *ServerTool);
	}

	// Call the interactable's server-side logic directly using tool validation
	if (InteractableComponent->GetOwnerRole() == ROLE_Authority)
	{
		// Validate interaction using the client's tool ID
		if (InteractableComponent->ValidateInteractionWithTool(OwnerCharacter, EquippedToolID))
		{
			UE_LOG(LogBlackTideGathering, Warning, TEXT("✅ SERVER: Interaction validated, starting..."));

			// Check if this is a resource node
			if (AActor* TargetActor = InteractableComponent->GetOwner())
			{
				UE_LOG(LogBlackTideGathering, Warning, TEXT("🎯 SERVER: Target actor: %s"), *TargetActor->GetName());

				// Bypass normal StartInteraction validation since we already validated with weapon-aware logic
				// Directly trigger the interaction completion for immediate resource harvesting
				if (ABlackTideResourceNode* ResourceNode = Cast<ABlackTideResourceNode>(TargetActor))
				{
					UE_LOG(LogBlackTideGathering, Warning, TEXT("🌳 SERVER: Directly harvesting resource node"));
					ResourceNode->HarvestResource(OwnerCharacter);
				}
				else
				{
					// For non-resource nodes, try normal StartInteraction
					bool bInteractionStarted = InteractableComponent->StartInteraction(OwnerCharacter);
					UE_LOG(LogBlackTideGathering, Warning, TEXT("🎯 SERVER: StartInteraction result: %s"),
						bInteractionStarted ? TEXT("SUCCESS") : TEXT("FAILED"));
				}
			}
		}
		else
		{
			UE_LOG(LogBlackTideGathering, Warning, TEXT("❌ SERVER: Interaction validation failed with tool '%s'"), *EquippedToolID);
		}
	}
	else
	{
		UE_LOG(LogBlackTideGathering, Warning, TEXT("❌ SERVER: Interactable is not on authority"));
	}
}
