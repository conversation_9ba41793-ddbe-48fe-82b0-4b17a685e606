// Copyright Epic Games, Inc. All Rights Reserved.

#include "BlackTideWorldItem.h"
#include "../BlackTideCharacter.h"
#include "../InventoryComponent.h"
#include "../ItemDatabase.h"
#include "../Components/BlackTideInteractableComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "../Components/BlackTideEquipmentComponent.h"

DEFINE_LOG_CATEGORY_STATIC(LogBlackTideWorldItem, Log, All);

ABlackTideWorldItem::ABlackTideWorldItem()
{
	PrimaryActorTick.bCanEverTick = false;

	// Enable replication
	bReplicates = true;
	SetReplicateMovement(true);

	// Create mesh component
	ItemMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("ItemMesh"));
	RootComponent = ItemMesh;

	// Create interactable component for E-key interaction
	InteractableComponent = CreateDefaultSubobject<UBlackTideInteractableComponent>(TEXT("InteractableComponent"));

	// Setup collision for pickup
	ItemMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	ItemMesh->SetCollisionObjectType(ECC_WorldDynamic);
	ItemMesh->SetCollisionResponseToAllChannels(ECR_Block);
	ItemMesh->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap); // Allow overlap for pickup

	// Enable physics
	ItemMesh->SetSimulatePhysics(true);
	ItemMesh->SetMassOverrideInKg(NAME_None, 10.0f); // Light items

	// Set default values
	LifeTime = 300.0f; // 5 minutes
	bCanBePickedUp = true;
}

void ABlackTideWorldItem::BeginPlay()
{
	Super::BeginPlay();

	UE_LOG(LogBlackTideWorldItem, Warning, TEXT("🪵 World item spawned: %s (Role: %s)"),
		*ItemData.Name, GetLocalRole() == ROLE_Authority ? TEXT("Server") : TEXT("Client"));

	// Start despawn timer
	if (LifeTime > 0.0f)
	{
		GetWorld()->GetTimerManager().SetTimer(
			DespawnTimer,
			this,
			&ABlackTideWorldItem::DespawnItem,
			LifeTime,
			false
		);
	}

	// Setup interactable component
	if (InteractableComponent)
	{
		InteractableComponent->InteractionText = FString::Printf(TEXT("Pick up %s"), *ItemData.Name);
		InteractableComponent->bIsInteractable = true;
		InteractableComponent->InteractionRange = 200.0f;
		InteractableComponent->InteractionDuration = 0.1f; // Quick pickup
		InteractableComponent->bRequiresTool = false; // No tool required for pickup

		// Bind interaction events
		InteractableComponent->OnInteractionCompleted.AddDynamic(this, &ABlackTideWorldItem::OnInteractionCompleted);
	}

	// Disable automatic overlap pickup - use E key interaction instead
	// if (ItemMesh)
	// {
	//     ItemMesh->OnComponentBeginOverlap.AddDynamic(this, &ABlackTideWorldItem::OnOverlapBegin);
	// }
}

void ABlackTideWorldItem::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ABlackTideWorldItem, ItemData);
}

void ABlackTideWorldItem::OnRep_ItemData()
{
	// Update mesh when item data replicates
	if (ItemMesh && ItemData.IsValid())
	{
		UStaticMesh* DataTableMesh = UItemDatabase::GetItemWorldMesh(ItemData.ItemID);
		if (DataTableMesh)
		{
			ItemMesh->SetStaticMesh(DataTableMesh);
			UE_LOG(LogBlackTideWorldItem, Log, TEXT("OnRep: Updated mesh for replicated item: %s"), *ItemData.Name);
		}

		// Apply scale from DataTable (using relative scale to match preview)
		FVector RelativeScale = UItemDatabase::GetItemWorldScale(ItemData.ItemID);
		ItemMesh->SetRelativeScale3D(RelativeScale);
		UE_LOG(LogBlackTideWorldItem, Log, TEXT("OnRep: Applied relative scale %s to replicated item: %s"),
			*RelativeScale.ToString(), *ItemData.Name);
	}
}

void ABlackTideWorldItem::SetupItem(const FInventoryItem& Item, UStaticMesh* Mesh)
{
	ItemData = Item;

	if (Mesh && ItemMesh)
	{
		ItemMesh->SetStaticMesh(Mesh);
		UE_LOG(LogBlackTideWorldItem, Log, TEXT("Set mesh for world item: %s"), *Item.Name);
	}
	else if (ItemMesh)
	{
		// Try to get mesh from ItemDataTable
		UStaticMesh* DataTableMesh = UItemDatabase::GetItemWorldMesh(Item.ItemID);
		if (DataTableMesh)
		{
			ItemMesh->SetStaticMesh(DataTableMesh);
			UE_LOG(LogBlackTideWorldItem, Log, TEXT("Set mesh from DataTable for: %s"), *Item.Name);
		}
		else
		{
			UE_LOG(LogBlackTideWorldItem, Warning, TEXT("No mesh found for world item: %s"), *Item.Name);
		}
	}

	// Apply scale from DataTable (using relative scale to match preview)
	if (ItemMesh)
	{
		FVector RelativeScale = UItemDatabase::GetItemWorldScale(Item.ItemID);
		ItemMesh->SetRelativeScale3D(RelativeScale);
		UE_LOG(LogBlackTideWorldItem, Log, TEXT("Applied relative scale %s to world item: %s"),
			*RelativeScale.ToString(), *Item.Name);
	}
}

void ABlackTideWorldItem::PickupItem(ABlackTideCharacter* Character)
{
	if (!Character || !bCanBePickedUp || !ItemData.IsValid())
	{
		return;
	}

	// Client calls server
	if (GetLocalRole() != ROLE_Authority)
	{
		UE_LOG(LogBlackTideWorldItem, Log, TEXT("Client requesting pickup of %s"), *ItemData.Name);
		ServerPickupItem(Character);
		return;
	}

	// Server execution
	UE_LOG(LogBlackTideWorldItem, Log, TEXT("Server processing pickup of %s"), *ItemData.Name);

	UInventoryComponent* Inventory = Character->GetInventoryComponent();
	if (!Inventory)
	{
		UE_LOG(LogBlackTideWorldItem, Warning, TEXT("Character has no inventory component"));
		return;
	}

	// Try to add to inventory
	int32 SlotIndex;
	if (Inventory->AddItem(ItemData, SlotIndex))
	{
		UE_LOG(LogBlackTideWorldItem, Log, TEXT("Player picked up world item: %s x%d"),
			*ItemData.Name, ItemData.CurrentStackSize);

		// Auto-carry wood/logs to shoulder when picked up
		if (ItemData.ItemType == EItemType::Resource &&
			(ItemData.ItemID.Contains(TEXT("wood")) || ItemData.ItemID.Contains(TEXT("log"))))
		{
			UBlackTideEquipmentComponent* EquipmentComponent = Character->GetEquipmentComponent();
			if (EquipmentComponent)
			{
				// Carry wood/logs on shoulder_r (Shoulder slot)
				EEquipmentSlot TargetSlot = EEquipmentSlot::Shoulder;

				if (EquipmentComponent->CanEquipItem(ItemData, TargetSlot))
				{
					// Remove from inventory first
					Inventory->RemoveItem(SlotIndex, 1);

					// Then carry on shoulder
					bool bEquipped = EquipmentComponent->EquipItem(ItemData, TargetSlot);
					UE_LOG(LogBlackTideWorldItem, Log, TEXT("Auto-carried %s to shoulder (slot %d): %s"),
						*ItemData.Name, (int32)TargetSlot, bEquipped ? TEXT("SUCCESS") : TEXT("FAILED"));
				}
				else
				{
					UE_LOG(LogBlackTideWorldItem, Warning, TEXT("Cannot carry %s to shoulder - slot occupied"), *ItemData.Name);
				}
			}
		}

		// Destroy the world item
		Destroy();
	}
	else
	{
		UE_LOG(LogBlackTideWorldItem, Warning, TEXT("Player inventory full - cannot pickup %s"), *ItemData.Name);
		// Could show UI message here
	}
}

void ABlackTideWorldItem::ServerPickupItem_Implementation(ABlackTideCharacter* Character)
{
	PickupItem(Character);
}

bool ABlackTideWorldItem::ServerPickupItem_Validate(ABlackTideCharacter* Character)
{
	return Character != nullptr && bCanBePickedUp && ItemData.IsValid();
}

void ABlackTideWorldItem::OnInteractionCompleted(ABlackTideCharacter* Character)
{
	UE_LOG(LogBlackTideWorldItem, Log, TEXT("Interaction completed - picking up world item"));
	PickupItem(Character);
}

void ABlackTideWorldItem::DespawnItem()
{
	UE_LOG(LogBlackTideWorldItem, Log, TEXT("World item despawning after %.1f seconds: %s"), LifeTime, *ItemData.Name);
	Destroy();
}

void ABlackTideWorldItem::OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
	UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	// Check if player overlapped
	ABlackTideCharacter* Character = Cast<ABlackTideCharacter>(OtherActor);
	if (Character && bCanBePickedUp && ItemData.IsValid())
	{
		UE_LOG(LogBlackTideWorldItem, Log, TEXT("Player %s overlapped world item: %s"),
			*Character->GetName(), *ItemData.Name);

		// Determine pickup method based on item carry type
		EItemCarryType CarryType = UItemDatabase::GetItemCarryType(ItemData.ItemID);

		switch (CarryType)
		{
			case EItemCarryType::InventoryOnly:
				// Traditional pickup to inventory
				PickupToInventory(Character);
				break;

			case EItemCarryType::CarryOnly:
				// Carry item (logs, planks, etc.)
				PickupToCarry(Character);
				break;

			case EItemCarryType::Both:
				// Default to inventory (can be extended later)
				PickupToInventory(Character);
				break;
		}
	}
}

void ABlackTideWorldItem::PickupToInventory(ABlackTideCharacter* Character)
{
	if (!Character || !bCanBePickedUp || !ItemData.IsValid())
	{
		return;
	}

	// Use existing PickupItem logic (goes to inventory)
	PickupItem(Character);
}

void ABlackTideWorldItem::PickupToCarry(ABlackTideCharacter* Character)
{
	if (!Character || !bCanBePickedUp || !ItemData.IsValid())
	{
		return;
	}

	// Client calls server
	if (GetLocalRole() != ROLE_Authority)
	{
		UE_LOG(LogBlackTideWorldItem, Log, TEXT("Client requesting carry pickup of %s"), *ItemData.Name);
		// TODO: Add ServerPickupToCarry RPC
		return;
	}

	// Server execution - carry to equipment slot
	UE_LOG(LogBlackTideWorldItem, Log, TEXT("Server processing carry pickup of %s"), *ItemData.Name);

	UBlackTideEquipmentComponent* EquipmentComponent = Character->GetEquipmentComponent();
	if (!EquipmentComponent)
	{
		UE_LOG(LogBlackTideWorldItem, Warning, TEXT("Character has no equipment component"));
		return;
	}

	// Get preferred carry slot for this item
	EEquipmentSlot PreferredSlot = UItemDatabase::GetPreferredCarrySlot(ItemData.ItemID);

	// Try to equip to carry slot
	if (EquipmentComponent->CanEquipItem(ItemData, PreferredSlot))
	{
		bool bSuccess = EquipmentComponent->EquipItem(ItemData, PreferredSlot);
		if (bSuccess)
		{
			UE_LOG(LogBlackTideWorldItem, Log, TEXT("Player carried world item: %s to slot %d"),
				*ItemData.Name, (int32)PreferredSlot);

			// Destroy the world item
			Destroy();
		}
		else
		{
			UE_LOG(LogBlackTideWorldItem, Warning, TEXT("Failed to carry item %s"), *ItemData.Name);
		}
	}
	else
	{
		UE_LOG(LogBlackTideWorldItem, Warning, TEXT("Cannot carry %s - slot %d occupied or incompatible"),
			*ItemData.Name, (int32)PreferredSlot);
		// Could show UI message here
	}
}
