// Copyright Epic Games, Inc. All Rights Reserved.

#include "BlackTideResourceNode.h"
#include "../Components/BlackTideInteractableComponent.h"
#include "../BlackTideCharacter.h"
#include "../InventoryComponent.h"
#include "../ItemDatabase.h"
#include "BlackTideWorldItem.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Components/PrimitiveComponent.h"
#include "EngineUtils.h"

DEFINE_LOG_CATEGORY_STATIC(LogBlackTideResource, Log, All);

ABlackTideResourceNode::ABlackTideResourceNode()
{
	PrimaryActorTick.bCanEverTick = false;
	bReplicates = true;

	// Create components
	MeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("MeshComponent"));
	RootComponent = MeshComponent;

	// Setup collision for resource nodes to be hit by weapons
	MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	MeshComponent->SetCollisionObjectType(ECC_WorldStatic);
	MeshComponent->SetCollisionResponseToAllChannels(ECR_Block);
	MeshComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
	MeshComponent->SetCollisionResponseToChannel(ECC_WorldDynamic, ECR_Block); // Important for weapon hits!

	UE_LOG(LogBlackTideResource, Warning, TEXT("🔧 RESOURCE NODE: Set collision to WorldStatic with weapon hit support"));

	InteractableComponent = CreateDefaultSubobject<UBlackTideInteractableComponent>(TEXT("InteractableComponent"));

	// Set default values
	CurrentHarvestCount = 0;
	bIsHarvested = false;
	LastHarvestTime = 0.0f;
	OriginalMesh = nullptr;
	OriginalScale = FVector::OneVector;

	// Tree falling defaults
	bEnableTreeFalling = true;
	FallForceMultiplier = 500.0f;
	FallDuration = 3.0f;

	// Log spawning defaults
	bSpawnLogsOnFall = true;
	LogActorClass = nullptr;
	MinLogsToSpawn = 2;
	MaxLogsToSpawn = 4;
}

ABlackTideResourceNode::~ABlackTideResourceNode()
{
	// Clean up any active timers to prevent callbacks on destroyed objects
	if (UWorld* World = GetWorld())
	{
		if (World->GetTimerManager().IsTimerActive(RespawnTimerHandle))
		{
			World->GetTimerManager().ClearTimer(RespawnTimerHandle);
			UE_LOG(LogBlackTideResource, Log, TEXT("Cleared respawn timer for destroyed ResourceNode %s"), *GetName());
		}

		if (World->GetTimerManager().IsTimerActive(TreeFallTimerHandle))
		{
			World->GetTimerManager().ClearTimer(TreeFallTimerHandle);
			UE_LOG(LogBlackTideResource, Log, TEXT("Cleared tree fall timer for destroyed ResourceNode %s"), *GetName());
		}
	}
}

void ABlackTideResourceNode::BeginPlay()
{
	Super::BeginPlay();

	// Store original mesh and scale with better validation
	if (MeshComponent && IsValid(MeshComponent))
	{
		UStaticMesh* CurrentMesh = MeshComponent->GetStaticMesh();
		if (CurrentMesh && IsValid(CurrentMesh))
		{
			OriginalMesh = CurrentMesh;
			OriginalScale = GetActorScale3D();
			UE_LOG(LogBlackTideResource, Log, TEXT("Stored original mesh %s for ResourceNode %s"),
				*OriginalMesh->GetName(), *GetName());
		}
		else
		{
			UE_LOG(LogBlackTideResource, Warning, TEXT("ResourceNode %s has no valid static mesh set"), *GetName());
		}
	}
	else
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("ResourceNode %s has no valid MeshComponent"), *GetName());
	}

	// Bind to interaction events
	if (InteractableComponent)
	{
		InteractableComponent->OnInteractionCompleted.AddDynamic(this, &ABlackTideResourceNode::OnInteractionCompleted);
		
		// Set interaction text based on resource type
		// NOTE: Tool requirements are now set in Blueprint only - don't override here!
		switch (ResourceType)
		{
		case EResourceNodeType::Tree:
			InteractableComponent->InteractionText = TEXT("Chop Tree");
			// Don't override Blueprint tool settings
			// InteractableComponent->bRequiresTool = true;
			// InteractableComponent->RequiredToolID = TEXT("IronAxe");
			break;
		case EResourceNodeType::Rock:
			InteractableComponent->InteractionText = TEXT("Mine Rock");
			// Don't override Blueprint tool settings
			// InteractableComponent->bRequiresTool = true;
			// InteractableComponent->RequiredToolID = TEXT("IronPickaxe");
			break;
		case EResourceNodeType::Plant:
			InteractableComponent->InteractionText = TEXT("Gather Plant");
			break;
		case EResourceNodeType::Bush:
			InteractableComponent->InteractionText = TEXT("Gather from Bush");
			break;
		default:
			InteractableComponent->InteractionText = TEXT("Gather Resource");
			break;
		}
	}

	UE_LOG(LogBlackTideResource, Log, TEXT("ResourceNode %s initialized with %d resource drops"), 
		*GetName(), ResourceDrops.Num());
}

void ABlackTideResourceNode::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ABlackTideResourceNode, CurrentHarvestCount);
	DOREPLIFETIME(ABlackTideResourceNode, bIsHarvested);
	DOREPLIFETIME(ABlackTideResourceNode, bIsBeingHarvested);
	DOREPLIFETIME(ABlackTideResourceNode, bIsFalling);
	DOREPLIFETIME(ABlackTideResourceNode, TreeFallDirection);
}

void ABlackTideResourceNode::OnInteractionCompleted(ABlackTideCharacter* Character)
{
	if (!Character)
	{
		return;
	}

	// Only process on server
	if (GetLocalRole() != ROLE_Authority)
	{
		return;
	}

	HarvestResource(Character);
}

void ABlackTideResourceNode::HarvestResource(ABlackTideCharacter* HarvesterCharacter)
{
	UE_LOG(LogBlackTideResource, Warning, TEXT("🌳 SERVER: HarvestResource called for %s by %s"),
		*GetName(), HarvesterCharacter ? *HarvesterCharacter->GetName() : TEXT("NULL"));

	// Only server can harvest
	if (GetLocalRole() != ROLE_Authority)
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("HarvestResource called on client - ignoring"));
		return;
	}

	if (!CanBeHarvested() || !HarvesterCharacter)
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("❌ SERVER: Cannot harvest resource - conditions not met (CanBeHarvested: %s, Character: %s)"),
			CanBeHarvested() ? TEXT("YES") : TEXT("NO"), HarvesterCharacter ? TEXT("YES") : TEXT("NO"));
		return;
	}

	// Race condition protection - check if someone is already harvesting
	if (bIsBeingHarvested)
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("Resource is already being harvested by another player"));
		return;
	}

	// Mark as being harvested to prevent race conditions
	bIsBeingHarvested = true;

	UInventoryComponent* Inventory = HarvesterCharacter->GetInventoryComponent();
	if (!Inventory)
	{
		bIsBeingHarvested = false; // Reset flag
		UE_LOG(LogBlackTideResource, Warning, TEXT("Harvester has no inventory component"));
		return;
	}

	// For trees: No direct items when chopping, items come from falling tree
	// For other resources: Give items normally
	int32 ItemsAdded = 0;

	if (ResourceType != EResourceNodeType::Tree)
	{
		// Generate resource drops for non-tree resources
		TArray<FInventoryItem> DroppedItems = GenerateResourceDrops();

		// Try to add items to inventory
		for (const FInventoryItem& Item : DroppedItems)
		{
			int32 SlotIndex;
			if (Inventory->AddItem(Item, SlotIndex))
			{
				ItemsAdded++;
				UE_LOG(LogBlackTideResource, Log, TEXT("Added %s x%d to inventory"),
					*Item.Name, Item.CurrentStackSize);
			}
			else
			{
				UE_LOG(LogBlackTideResource, Warning, TEXT("Failed to add %s to inventory - full?"), *Item.Name);
			}
		}
	}
	else
	{
		// Trees: Just damage them, no direct items
		UE_LOG(LogBlackTideResource, Log, TEXT("🌳 Tree damaged - no direct items (items come from falling tree)"));
		ItemsAdded = 1; // Fake success to continue the harvest logic
	}

	// Always reset the harvesting flag
	bIsBeingHarvested = false;

	if (ItemsAdded > 0)
	{
		// Update harvest state
		CurrentHarvestCount++;
		LastHarvestTime = GetWorld()->GetTimeSeconds();

		// Check if resource is depleted
		if (CurrentHarvestCount >= MaxHarvestCount)
		{
			bIsHarvested = true;

			// Start tree falling animation for trees
			if (ResourceType == EResourceNodeType::Tree && bEnableTreeFalling)
			{
				StartTreeFalling(HarvesterCharacter);
			}
			else
			{
				UpdateVisualState();
			}

			if (bRespawns)
			{
				StartRespawnTimer();
			}
			else if (bDestroyOnHarvest)
			{
				Destroy();
				return;
			}
		}

		UE_LOG(LogBlackTideResource, Log, TEXT("Resource harvested by %s. Count: %d/%d"),
			*HarvesterCharacter->GetName(), CurrentHarvestCount, MaxHarvestCount);

		// Broadcast Blueprint event
		OnResourceHarvested(HarvesterCharacter);
	}
}

bool ABlackTideResourceNode::CanBeHarvested() const
{
	return !bIsHarvested && !bIsBeingHarvested && CurrentHarvestCount < MaxHarvestCount;
}

float ABlackTideResourceNode::GetRespawnProgress() const
{
	if (!bIsHarvested || !bRespawns || RespawnTime <= 0.0f)
	{
		return 0.0f;
	}

	float CurrentTime = GetWorld()->GetTimeSeconds();
	float ElapsedTime = CurrentTime - LastHarvestTime;
	return FMath::Clamp(ElapsedTime / RespawnTime, 0.0f, 1.0f);
}

TArray<FInventoryItem> ABlackTideResourceNode::GenerateResourceDrops() const
{
	TArray<FInventoryItem> DroppedItems;

	for (const FResourceDrop& Drop : ResourceDrops)
	{
		// Check drop chance
		if (FMath::RandRange(0.0f, 1.0f) <= Drop.DropChance)
		{
			// Calculate amount
			int32 Amount = FMath::RandRange(Drop.MinAmount, Drop.MaxAmount);
			
			// Create item
			FInventoryItem Item = UItemDatabase::CreateItem(Drop.ItemID, Amount);
			if (Item.IsValid())
			{
				DroppedItems.Add(Item);
			}
		}
	}

	return DroppedItems;
}

void ABlackTideResourceNode::ResetResource()
{
	if (GetLocalRole() != ROLE_Authority)
	{
		return;
	}

	CurrentHarvestCount = 0;
	bIsHarvested = false;
	bIsBeingHarvested = false; // Reset harvesting flag
	LastHarvestTime = 0.0f;

	// Clear respawn timer
	GetWorld()->GetTimerManager().ClearTimer(RespawnTimerHandle);

	// Update visual state on all clients
	MulticastUpdateVisualState();

	UE_LOG(LogBlackTideResource, Log, TEXT("Resource %s has respawned"), *GetName());

	// Broadcast Blueprint event
	OnResourceRespawned();
}

void ABlackTideResourceNode::OnRep_IsHarvested()
{
	UE_LOG(LogBlackTideResource, Warning, TEXT("🌐 Resource %s harvested state replicated: %s (Role: %s)"),
		*GetName(), bIsHarvested ? TEXT("Harvested") : TEXT("Available"),
		GetLocalRole() == ROLE_Authority ? TEXT("Server") : TEXT("Client"));

	// DON'T update visual state for trees that should fall - let the falling animation handle it
	if (bIsHarvested && ResourceType == EResourceNodeType::Tree && bEnableTreeFalling)
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("🌳 Tree harvested - waiting for falling animation, NOT updating visual state"));
		// Tree falling animation will handle the visual state
		return;
	}

	// Update visual state for non-falling resources
	UpdateVisualState();
}

void ABlackTideResourceNode::OnRep_TreeFallingState()
{
	UE_LOG(LogBlackTideResource, Warning, TEXT("🌐 Tree falling state replicated: %s (Role: %s)"),
		bIsFalling ? TEXT("Falling") : TEXT("Standing"),
		GetLocalRole() == ROLE_Authority ? TEXT("Server") : TEXT("Client"));

	// If tree is falling, enable movement replication
	if (bIsFalling)
	{
		SetReplicateMovement(true);
		UE_LOG(LogBlackTideResource, Warning, TEXT("🌐 Enabled movement replication for falling tree"));

		// Start falling animation on clients (backup in case multicast didn't work)
		if (GetLocalRole() != ROLE_Authority)
		{
			UE_LOG(LogBlackTideResource, Warning, TEXT("🌐 Client starting tree fall animation via OnRep with direction: %s"), *TreeFallDirection.ToString());
			MulticastStartTreeFalling_Implementation(TreeFallDirection, nullptr);
		}
	}
}

void ABlackTideResourceNode::UpdateVisualState()
{
	// Add comprehensive safety checks
	if (!IsValid(this) || !MeshComponent || !IsValid(MeshComponent))
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("UpdateVisualState: Invalid object or MeshComponent"));
		return;
	}

	if (bIsHarvested)
	{
		// Switch to harvested appearance
		if (HarvestedMesh && IsValid(HarvestedMesh))
		{
			MeshComponent->SetStaticMesh(HarvestedMesh);
		}
		SetActorScale3D(HarvestedScale);

		// Disable interaction
		if (InteractableComponent && IsValid(InteractableComponent))
		{
			InteractableComponent->bIsInteractable = false;
		}
	}
	else
	{
		// Restore original appearance
		if (OriginalMesh && IsValid(OriginalMesh))
		{
			MeshComponent->SetStaticMesh(OriginalMesh);
		}
		else
		{
			UE_LOG(LogBlackTideResource, Warning, TEXT("UpdateVisualState: OriginalMesh is null or invalid for %s"), *GetName());
		}
		SetActorScale3D(OriginalScale);

		// Enable interaction
		if (InteractableComponent && IsValid(InteractableComponent))
		{
			InteractableComponent->bIsInteractable = true;
		}
	}
}

void ABlackTideResourceNode::StartRespawnTimer()
{
	if (RespawnTime > 0.0f)
	{
		GetWorld()->GetTimerManager().SetTimer(
			RespawnTimerHandle,
			this,
			&ABlackTideResourceNode::ResetResource,
			RespawnTime,
			false
		);

		UE_LOG(LogBlackTideResource, Log, TEXT("Respawn timer started for %s (%.1f seconds)"),
			*GetName(), RespawnTime);
	}
}

// Blueprint configuration helpers
void ABlackTideResourceNode::SetupAsTree(int32 HarvestCount, float RespawnTimeSeconds)
{
	ResourceType = EResourceNodeType::Tree;
	MaxHarvestCount = HarvestCount;
	RespawnTime = RespawnTimeSeconds;

	if (InteractableComponent)
	{
		InteractableComponent->InteractionText = TEXT("Chop Tree");
		// Don't override Blueprint tool settings
		// InteractableComponent->bRequiresTool = true;
		// InteractableComponent->RequiredToolID = TEXT("IronAxe");
		InteractableComponent->InteractionDuration = 3.0f;
	}

	// Only add default drops if none are configured (preserves Blueprint settings)
	if (ResourceDrops.Num() == 0)
	{
		AddResourceDrop(TEXT("wood"), 3, 5, 1.0f);
		AddResourceDrop(TEXT("sticks"), 2, 4, 0.8f);
		UE_LOG(LogBlackTideResource, Log, TEXT("Added default tree drops for %s"), *GetName());
	}
	else
	{
		UE_LOG(LogBlackTideResource, Log, TEXT("Using existing %d resource drops for %s"), ResourceDrops.Num(), *GetName());
	}

	UE_LOG(LogBlackTideResource, Log, TEXT("Configured %s as Tree"), *GetName());
}

void ABlackTideResourceNode::SetupAsRock(int32 HarvestCount, float RespawnTimeSeconds)
{
	ResourceType = EResourceNodeType::Rock;
	MaxHarvestCount = HarvestCount;
	RespawnTime = RespawnTimeSeconds;

	if (InteractableComponent)
	{
		InteractableComponent->InteractionText = TEXT("Mine Rock");
		// Don't override Blueprint tool settings
		// InteractableComponent->bRequiresTool = true;
		// InteractableComponent->RequiredToolID = TEXT("IronPickaxe");
		InteractableComponent->InteractionDuration = 4.0f;
	}

	// Only add default drops if none are configured (preserves Blueprint settings)
	if (ResourceDrops.Num() == 0)
	{
		AddResourceDrop(TEXT("stone"), 2, 4, 1.0f);
		AddResourceDrop(TEXT("flint"), 1, 2, 0.3f);
		UE_LOG(LogBlackTideResource, Log, TEXT("Added default rock drops for %s"), *GetName());
	}
	else
	{
		UE_LOG(LogBlackTideResource, Log, TEXT("Using existing %d resource drops for %s"), ResourceDrops.Num(), *GetName());
	}

	UE_LOG(LogBlackTideResource, Log, TEXT("Configured %s as Rock"), *GetName());
}

void ABlackTideResourceNode::SetupAsPlant(int32 HarvestCount, float RespawnTimeSeconds)
{
	ResourceType = EResourceNodeType::Plant;
	MaxHarvestCount = HarvestCount;
	RespawnTime = RespawnTimeSeconds;

	if (InteractableComponent)
	{
		InteractableComponent->InteractionText = TEXT("Gather Plant");
		InteractableComponent->bRequiresTool = false;
		InteractableComponent->InteractionDuration = 2.0f;
	}

	// Only add default drops if none are configured (preserves Blueprint settings)
	if (ResourceDrops.Num() == 0)
	{
		AddResourceDrop(TEXT("yucca_root"), 1, 3, 1.0f);
		AddResourceDrop(TEXT("fiber"), 2, 5, 0.7f);
		UE_LOG(LogBlackTideResource, Log, TEXT("Added default plant drops for %s"), *GetName());
	}
	else
	{
		UE_LOG(LogBlackTideResource, Log, TEXT("Using existing %d resource drops for %s"), ResourceDrops.Num(), *GetName());
	}

	UE_LOG(LogBlackTideResource, Log, TEXT("Configured %s as Plant"), *GetName());
}

void ABlackTideResourceNode::SetupAsBush(int32 HarvestCount, float RespawnTimeSeconds)
{
	ResourceType = EResourceNodeType::Bush;
	MaxHarvestCount = HarvestCount;
	RespawnTime = RespawnTimeSeconds;

	if (InteractableComponent)
	{
		InteractableComponent->InteractionText = TEXT("Gather from Bush");
		InteractableComponent->bRequiresTool = false;
		InteractableComponent->InteractionDuration = 2.5f;
	}

	// Only add default drops if none are configured (preserves Blueprint settings)
	if (ResourceDrops.Num() == 0)
	{
		AddResourceDrop(TEXT("berries"), 3, 8, 1.0f);
		AddResourceDrop(TEXT("sticks"), 1, 3, 0.5f);
		UE_LOG(LogBlackTideResource, Log, TEXT("Added default bush drops for %s"), *GetName());
	}
	else
	{
		UE_LOG(LogBlackTideResource, Log, TEXT("Using existing %d resource drops for %s"), ResourceDrops.Num(), *GetName());
	}

	UE_LOG(LogBlackTideResource, Log, TEXT("Configured %s as Bush"), *GetName());
}

void ABlackTideResourceNode::AddResourceDrop(const FString& ItemID, int32 MinAmount, int32 MaxAmount, float DropChance)
{
	FResourceDrop NewDrop;
	NewDrop.ItemID = ItemID;
	NewDrop.MinAmount = MinAmount;
	NewDrop.MaxAmount = MaxAmount;
	NewDrop.DropChance = FMath::Clamp(DropChance, 0.0f, 1.0f);

	ResourceDrops.Add(NewDrop);

	UE_LOG(LogBlackTideResource, Log, TEXT("Added resource drop: %s (%d-%d, %.1f%% chance)"),
		*ItemID, MinAmount, MaxAmount, DropChance * 100.0f);
}

void ABlackTideResourceNode::StartTreeFalling(ABlackTideCharacter* FellerCharacter)
{
	// Add comprehensive safety checks
	if (!IsValid(this) || !MeshComponent || !IsValid(MeshComponent) || ResourceType != EResourceNodeType::Tree)
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("Cannot start tree falling - not a tree, no mesh, or object invalid"));
		UpdateVisualState(); // Fallback to normal visual update
		return;
	}

	// Only server should initiate the falling sequence
	if (GetLocalRole() != ROLE_Authority)
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("StartTreeFalling called on client - only server can initiate"));
		return;
	}

	UE_LOG(LogBlackTideResource, Warning, TEXT("🌳 Server starting tree falling animation for %s"), *GetName());

	// Calculate fall direction (away from the character who felled it)
	FVector FallDirection = FVector::ZeroVector;
	if (FellerCharacter)
	{
		FVector ToFeller = (FellerCharacter->GetActorLocation() - GetActorLocation()).GetSafeNormal();
		FallDirection = -ToFeller; // Fall away from the feller
		FallDirection.Z = 0.0f; // Keep it horizontal
		FallDirection = FallDirection.GetSafeNormal();
	}
	else
	{
		// Random fall direction if no feller
		FallDirection = FVector(FMath::RandRange(-1.0f, 1.0f), FMath::RandRange(-1.0f, 1.0f), 0.0f).GetSafeNormal();
	}

	// Store fall direction for replication
	TreeFallDirection = FallDirection;

	// Set falling state (this will replicate to all clients)
	bIsFalling = true;

	// Multicast the falling animation to all clients
	UE_LOG(LogBlackTideResource, Warning, TEXT("🌳 SERVER: Calling MulticastStartTreeFalling for %s"), *GetName());
	MulticastStartTreeFalling(FallDirection, FellerCharacter);
}

void ABlackTideResourceNode::MulticastStartTreeFalling_Implementation(const FVector& FallDirection, ABlackTideCharacter* FellerCharacter)
{
	// This runs on all clients (and server)
	UE_LOG(LogBlackTideResource, Warning, TEXT("🌳 Multicast tree falling animation for %s (Role: %s)"),
		*GetName(), GetLocalRole() == ROLE_Authority ? TEXT("Server") : TEXT("Client"));

	// Clear any existing tree fall timer
	if (UWorld* World = GetWorld())
	{
		if (World->GetTimerManager().IsTimerActive(TreeFallTimerHandle))
		{
			World->GetTimerManager().ClearTimer(TreeFallTimerHandle);
			UE_LOG(LogBlackTideResource, Warning, TEXT("Cleared existing tree fall timer for %s"), *GetName());
		}
	}

	// Store original transform
	FVector OriginalLocation = GetActorLocation();
	FRotator OriginalRotation = GetActorRotation();

	// Calculate final fallen position and rotation
	FVector FinalLocation = OriginalLocation + (FallDirection * 400.0f); // Fall 4 meters away
	FinalLocation.Z = OriginalLocation.Z - 50.0f; // Lower to ground level

	FRotator FinalRotation = OriginalRotation;
	FinalRotation.Pitch = 85.0f; // Almost horizontal but not completely flat
	FinalRotation.Yaw += FMath::RandRange(-45.0f, 45.0f); // More random variation

	UE_LOG(LogBlackTideResource, Warning, TEXT("🌳 Tree will fall from %s to %s"),
		*OriginalLocation.ToString(), *FinalLocation.ToString());

	// Simple timer-based animation using member variable
	float ElapsedTime = 0.0f;

	// Use weak pointer to safely capture this object
	TWeakObjectPtr<ABlackTideResourceNode> WeakThis(this);

	GetWorld()->GetTimerManager().SetTimer(TreeFallTimerHandle, [WeakThis, OriginalLocation, OriginalRotation, FinalLocation, FinalRotation, ElapsedTime]() mutable
	{
		// Check if the object is still valid
		if (!WeakThis.IsValid())
		{
			UE_LOG(LogBlackTideResource, Warning, TEXT("🌳 Tree fall animation cancelled - object destroyed"));
			return;
		}

		ABlackTideResourceNode* ResourceNode = WeakThis.Get();
		if (!ResourceNode || !IsValid(ResourceNode))
		{
			UE_LOG(LogBlackTideResource, Warning, TEXT("🌳 Tree fall animation cancelled - object invalid"));
			return;
		}

		ElapsedTime += 0.016f; // Update at ~60 FPS (1/60 = 0.016)
		float Alpha = FMath::Clamp(ElapsedTime / ResourceNode->FallDuration, 0.0f, 1.0f);

		// Smooth interpolation
		Alpha = FMath::SmoothStep(0.0f, 1.0f, Alpha);

		// Interpolate position and rotation
		FVector CurrentLocation = FMath::Lerp(OriginalLocation, FinalLocation, Alpha);
		FRotator CurrentRotation = FMath::Lerp(OriginalRotation, FinalRotation, Alpha);

		ResourceNode->SetActorLocationAndRotation(CurrentLocation, CurrentRotation);

		UE_LOG(LogBlackTideResource, Log, TEXT("🌳 Fall progress: %.1f%%, Location: %s"),
			Alpha * 100.0f, *CurrentLocation.ToString());

		// When animation is complete
		if (Alpha >= 1.0f)
		{
			if (UWorld* World = ResourceNode->GetWorld())
			{
				World->GetTimerManager().ClearTimer(ResourceNode->TreeFallTimerHandle);
			}

			UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 Tree fall complete! Final location: %s"), *FinalLocation.ToString());

			// Only server spawns actual log items
			if (ResourceNode->GetLocalRole() == ROLE_Authority && ResourceNode->bSpawnLogsOnFall)
			{
				UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 Server spawning logs..."));
				ResourceNode->SpawnLogsAtLocation(FinalLocation);

				// Multicast visual effects for log spawning
				int32 NumLogs = FMath::RandRange(ResourceNode->MinLogsToSpawn, ResourceNode->MaxLogsToSpawn);
				ResourceNode->MulticastSpawnLogEffects(FinalLocation, NumLogs);
			}

			// Don't update visual state for trees - keep them visible in fallen position
			// Trees should remain visible after falling, not disappear like other resources
			if (ResourceNode->ResourceType != EResourceNodeType::Tree)
			{
				ResourceNode->UpdateVisualState();
			}
			else
			{
				UE_LOG(LogBlackTideResource, Warning, TEXT("🌳 Tree remains visible in fallen position"));
			}

			UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 Tree fall animation complete"));
		}
	}, 0.016f, true); // Repeat at 60 FPS
}

void ABlackTideResourceNode::SpawnLogsAtLocation(const FVector& Location)
{
	// Only server should spawn logs
	if (GetLocalRole() != ROLE_Authority)
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 SpawnLogsAtLocation called on client - ignoring"));
		return;
	}

	// Spawn both logs and wood items from fallen trees
	int32 NumLogs = FMath::RandRange(MinLogsToSpawn, MaxLogsToSpawn);
	UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 SERVER: Spawning %d log items at %s"), NumLogs, *Location.ToString());

	// Spawn logs first (larger items)
	for (int32 i = 0; i < NumLogs; i++)
	{
		FVector SpawnLocation = Location + FVector(
			FMath::RandRange(-200.0f, 200.0f),
			FMath::RandRange(-200.0f, 200.0f),
			50.0f // Slightly above ground
		);

		// Try to spawn logs first, fallback to wood if logs don't exist
		ABlackTideWorldItem* LogItem = UItemDatabase::SpawnWorldItem(GetWorld(), TEXT("logs"), SpawnLocation, 1);
		if (LogItem)
		{
			UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 SERVER: Spawned log item %d at %s (bReplicates: %s)"),
				i + 1, *SpawnLocation.ToString(), LogItem->GetIsReplicated() ? TEXT("YES") : TEXT("NO"));
		}
		else
		{
			// Fallback to wood if logs item doesn't exist
			ABlackTideWorldItem* WoodItem = UItemDatabase::SpawnWorldItem(GetWorld(), TEXT("wood"), SpawnLocation, 2);
			if (WoodItem)
			{
				UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 SERVER: Spawned wood item %d (fallback) at %s (bReplicates: %s)"),
					i + 1, *SpawnLocation.ToString(), WoodItem->GetIsReplicated() ? TEXT("YES") : TEXT("NO"));
			}
			else
			{
				UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 SERVER: Failed to spawn any item %d"), i + 1);
			}
		}
	}

	// Also spawn some smaller wood pieces
	int32 NumWoodPieces = FMath::RandRange(2, 4);
	for (int32 i = 0; i < NumWoodPieces; i++)
	{
		FVector SpawnLocation = Location + FVector(
			FMath::RandRange(-150.0f, 150.0f),
			FMath::RandRange(-150.0f, 150.0f),
			30.0f // Slightly above ground
		);

		ABlackTideWorldItem* WoodItem = UItemDatabase::SpawnWorldItem(GetWorld(), TEXT("wood"), SpawnLocation, 1);
		if (WoodItem)
		{
			UE_LOG(LogBlackTideResource, Log, TEXT("🪵 Spawned wood piece %d at %s"), i + 1, *SpawnLocation.ToString());
		}
	}

	UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 Spawned %d logs and %d wood pieces from fallen tree"), NumLogs, NumWoodPieces);
}

void ABlackTideResourceNode::MulticastSpawnLogEffects_Implementation(const FVector& Location, int32 NumLogs)
{
	// This runs on all clients for visual/audio effects only
	// The actual item spawning is handled by the server in SpawnLogsAtLocation
	UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 Multicast log effects at %s (%d logs)"), *Location.ToString(), NumLogs);

	// Here you could add:
	// - Particle effects for logs hitting the ground
	// - Sound effects for wood impacts
	// - Screen shake for nearby players
	// - Dust clouds, etc.

	// For now, just log the effect
	UE_LOG(LogBlackTideResource, Log, TEXT("🪵 Visual effects for %d logs spawning"), NumLogs);
}

void ABlackTideResourceNode::MulticastUpdateVisualState_Implementation()
{
	// This runs on all clients to update visual state
	UE_LOG(LogBlackTideResource, Log, TEXT("🎨 Multicast visual state update for %s"), *GetName());
	UpdateVisualState();
}
