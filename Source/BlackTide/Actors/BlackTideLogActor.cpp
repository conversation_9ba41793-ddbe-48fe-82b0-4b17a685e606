// Copyright Epic Games, Inc. All Rights Reserved.

#include "BlackTideLogActor.h"
#include "../BlackTideCharacter.h"
#include "../InventoryComponent.h"
#include "../ItemDatabase.h"
#include "../Components/BlackTideInteractableComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"

DEFINE_LOG_CATEGORY_STATIC(LogBlackTideLog, Log, All);

ABlackTideLogActor::ABlackTideLogActor()
{
	PrimaryActorTick.bCanEverTick = false;

	// Enable replication
	bReplicates = true;
	SetReplicateMovement(true);

	// Create mesh component
	LogMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("LogMesh"));
	RootComponent = LogMesh;

	// Create interactable component
	InteractableComponent = CreateDefaultSubobject<UBlackTideInteractableComponent>(TEXT("InteractableComponent"));

	// Setup collision for pickup - make sure it's detectable by line traces
	LogMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	LogMesh->SetCollisionObjectType(ECC_WorldDynamic);
	LogMesh->SetCollisionResponseToAllChannels(ECR_Block);
	LogMesh->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap); // Allow overlap for pickup
	LogMesh->SetCollisionResponseToChannel(ECC_Visibility, ECR_Block); // Make sure it's hit by line traces

	// Enable physics
	LogMesh->SetSimulatePhysics(true);
	LogMesh->SetMassOverrideInKg(NAME_None, 50.0f); // 50kg log

	// Set default values
	LogItemID = TEXT("wood");
	WoodAmount = 3;
	LifeTime = 300.0f; // 5 minutes
	bCanBePickedUp = true;
}

void ABlackTideLogActor::BeginPlay()
{
	Super::BeginPlay();

	UE_LOG(LogBlackTideLog, Log, TEXT("Log spawned: %s"), *GetName());

	// Start despawn timer
	if (LifeTime > 0.0f)
	{
		GetWorld()->GetTimerManager().SetTimer(
			DespawnTimer,
			this,
			&ABlackTideLogActor::DespawnLog,
			LifeTime,
			false
		);
	}

	// Setup interactable component
	if (InteractableComponent)
	{
		InteractableComponent->InteractionText = FString::Printf(TEXT("Pick up %s"), *LogItemID);
		InteractableComponent->bIsInteractable = true;
		InteractableComponent->InteractionRange = 200.0f;
		InteractableComponent->InteractionDuration = 0.1f; // Quick pickup
		InteractableComponent->bRequiresTool = false; // No tool required for pickup

		// Bind interaction events
		InteractableComponent->OnInteractionCompleted.AddDynamic(this, &ABlackTideLogActor::OnInteractionCompleted);
	}

	// Keep overlap as backup (but prefer interactable system)
	if (LogMesh)
	{
		LogMesh->OnComponentBeginOverlap.AddDynamic(this, &ABlackTideLogActor::OnOverlapBegin);
	}
}

void ABlackTideLogActor::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ABlackTideLogActor, LogItemID);
	DOREPLIFETIME(ABlackTideLogActor, WoodAmount);
	DOREPLIFETIME(ABlackTideLogActor, bCanBePickedUp);
}

void ABlackTideLogActor::PickupLog(ABlackTideCharacter* Character)
{
	if (!Character || !bCanBePickedUp)
	{
		return;
	}

	// Client calls server
	if (GetLocalRole() != ROLE_Authority)
	{
		UE_LOG(LogBlackTideLog, Log, TEXT("Client requesting pickup of log"));
		ServerPickupLog(Character);
		return;
	}

	// Server execution
	UE_LOG(LogBlackTideLog, Log, TEXT("Server processing pickup of log"));

	UInventoryComponent* Inventory = Character->GetInventoryComponent();
	if (!Inventory)
	{
		UE_LOG(LogBlackTideLog, Warning, TEXT("Character has no inventory component"));
		return;
	}

	// Create wood item
	FInventoryItem WoodItem = UItemDatabase::CreateItem(LogItemID, WoodAmount);
	if (!WoodItem.IsValid())
	{
		UE_LOG(LogBlackTideLog, Warning, TEXT("Failed to create wood item"));
		return;
	}

	// Try to add to inventory
	int32 SlotIndex;
	if (Inventory->AddItem(WoodItem, SlotIndex))
	{
		UE_LOG(LogBlackTideLog, Log, TEXT("Player picked up log: %s x%d"),
			*WoodItem.Name, WoodAmount);

		// Destroy the log
		Destroy();
	}
	else
	{
		UE_LOG(LogBlackTideLog, Warning, TEXT("Player inventory full - cannot pickup log"));
		// Could show UI message here
	}
}

void ABlackTideLogActor::ServerPickupLog_Implementation(ABlackTideCharacter* Character)
{
	PickupLog(Character);
}

bool ABlackTideLogActor::ServerPickupLog_Validate(ABlackTideCharacter* Character)
{
	return Character != nullptr && bCanBePickedUp;
}

void ABlackTideLogActor::OnInteractionCompleted(ABlackTideCharacter* Character, float InteractionDuration)
{
	UE_LOG(LogBlackTideLog, Log, TEXT("Interaction completed - picking up log"));
	PickupLog(Character);
}

void ABlackTideLogActor::DespawnLog()
{
	UE_LOG(LogBlackTideLog, Log, TEXT("Log despawning after %.1f seconds"), LifeTime);
	Destroy();
}

UFUNCTION()
void ABlackTideLogActor::OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
	UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	// Check if player overlapped
	ABlackTideCharacter* Character = Cast<ABlackTideCharacter>(OtherActor);
	if (Character && bCanBePickedUp)
	{
		UE_LOG(LogBlackTideLog, Log, TEXT("Player %s overlapped log"), *Character->GetName());
		PickupLog(Character);
	}
}
