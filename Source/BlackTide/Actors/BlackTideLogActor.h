// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "BlackTideLogActor.generated.h"

/**
 * Simple log actor that spawns when trees fall
 * Can be picked up as a resource
 */
UCLASS(BlueprintType, Blueprintable)
class BLACKTIDE_API ABlackTideLogActor : public AActor
{
	GENERATED_BODY()

public:
	ABlackTideLogActor();

protected:
	virtual void BeginPlay() override;
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	// Components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UStaticMeshComponent* LogMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	class UBlackTideInteractableComponent* InteractableComponent;

	// Settings (replicated so all clients can see the log properly)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Log Settings")
	FString LogItemID = TEXT("wood");

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Log Settings")
	int32 WoodAmount = 3;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Log Settings")
	float LifeTime = 300.0f; // 5 minutes before despawn

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Log Settings")
	bool bCanBePickedUp = true;

public:
	// Functions
	UFUNCTION(BlueprintCallable, Category = "Log")
	void PickupLog(class ABlackTideCharacter* Character);

	UFUNCTION(BlueprintCallable, Category = "Log")
	void DespawnLog();

	// Server functions for multiplayer
	UFUNCTION(Server, Reliable, WithValidation, Category = "Log")
	void ServerPickupLog(class ABlackTideCharacter* Character);

protected:
	// Timer for despawn
	FTimerHandle DespawnTimer;

	// Interaction events
	UFUNCTION()
	void OnInteractionCompleted(class ABlackTideCharacter* Character);

	// Overlap event (backup)
	UFUNCTION()
	void OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
		UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
};
